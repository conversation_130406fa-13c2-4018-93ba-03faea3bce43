# Animeko Web 端支持技术方案（Next.js 版）

> 经过深入的技术调研和实际验证，我们决定放弃 Kotlin Multiplatform (KMP) 方案，改用 Next.js + TypeScript 技术栈为 Animeko 项目增加 Web 端支持。

## 1. 项目背景与技术方案选择

### 1.1 Animeko 项目现状分析

Animeko 是一个基于 Kotlin Multiplatform (KMP) + Compose Multiplatform 构建的跨平台动漫播放器，目前支持 Android、Desktop 和 iOS 平台。项目采用 Android 官方推荐的分层架构：

```mermaid
graph TD
    subgraph "UI Layer"
        A[ui-foundation] --> B[ui-subject]
        A --> C[ui-exploration]
        A --> D[ui-settings]
        A --> E[video-player]
    end

    subgraph "Domain Layer"
        F[app-data/domain] --> G[Use Cases]
        F --> H[Business Logic]
    end

    subgraph "Data Layer"
        I[app-data/data] --> J[Repository]
        I --> K[Network]
        I --> L[Database]
    end

    subgraph "Platform Layer"
        M[app-platform] --> N[BuildConfig]
        M --> O[Permissions]
    end

    B --> F
    C --> F
    D --> F
    E --> F
    F --> I
```

### 1.2 为什么放弃 KMP 方案

经过深入的技术调研和实际验证，我们发现 KMP 方案在 Web 平台上存在以下问题：

#### 1.2.1 Kotlin/Wasm 的问题
- **生态系统不成熟**：大量核心依赖库（Koin、Ktor、Coil、Sentry）不支持 wasmJs 目标
- **浏览器兼容性有限**：Safari 浏览器不支持 WasmGC，影响 15-20% 用户
- **构建工具链不稳定**：频繁出现编译错误，开发体验差
- **调试工具不完善**：缺乏成熟的调试和开发工具

#### 1.2.2 Kotlin/JS 的问题
- **共享模块支持有限**：现有共享模块大多使用 `ani-mpp-lib-targets` 插件，仅支持 Android/Desktop/iOS
- **Compose HTML 功能受限**：无法实现复杂的 UI 效果，与其他平台差异显著
- **开发和维护成本高**：需要处理大量平台差异和兼容性问题

### 1.3 Next.js 方案的优势

**技术成熟度：**
- 成熟稳定的 Web 开发生态系统
- 丰富的 UI 组件库和开发工具
- 优秀的性能表现和 SEO 支持

**开发效率：**
- 开发效率远高于 KMP 方案
- 支持现代 Web 标准和最佳实践
- 更容易找到合适的开发人员

**业务逻辑复用：**
- 虽然无法直接复用 Kotlin 代码，但可以通过 REST API 复用后端业务逻辑
- 数据模型可以转换为 TypeScript 接口
- 认证和会话管理可以通过 API 实现

### 1.4 技术目标

- **功能一致性**：确保 Web 端与其他平台在核心功能上保持一致
- **数据同步**：通过 API 调用实现与其他平台的数据同步
- **性能优化**：确保 Web 端具有优秀的加载速度和运行性能
- **渐进式实现**：采用分阶段开发策略，优先实现核心功能

## 2. Animeko 项目架构深度分析

### 2.1 核心数据模型分析

基于对 Animeko 项目的深入分析，以下是需要在 Web 端复用的核心数据模型：

#### 2.1.1 番剧相关模型
```kotlin
// SubjectInfo - 番剧基本信息
data class SubjectInfo(
    val subjectId: Int,
    val subjectType: SubjectType,
    val name: String,
    val nameCn: String,
    val summary: String,
    val nsfw: Boolean,
    val imageLarge: String,
    val totalEpisodes: Int,
    val airDate: PackedDate,
    val tags: List<Tag>,
    val aliases: List<String>,
    val ratingInfo: RatingInfo,
    val collectionStats: SubjectCollectionStats
)

// EpisodeInfo - 剧集信息
data class EpisodeInfo(
    val episodeId: Int,
    val type: EpisodeType?,
    val name: String,
    val nameCn: String,
    val airDate: PackedDate,
    val comment: Int,
    val desc: String,
    val sort: EpisodeSort,
    val ep: EpisodeSort?
)
```

#### 2.1.2 用户相关模型
```kotlin
// SelfInfo - 当前用户信息
data class SelfInfo(
    val id: Uuid,
    val nickname: String,
    val email: String?,
    val hasPassword: Boolean,
    val avatarUrl: String?,
    val bangumiUsername: String?
)

// UserInfo - 用户信息
data class UserInfo(
    val id: Int,
    val username: String?,
    val nickname: String?,
    val avatarUrl: String?,
    val sign: String?
)
```

### 2.2 API 接口分析

#### 2.2.1 Bangumi API
- **番剧信息查询**：获取番剧详情、剧集列表
- **用户收藏管理**：收藏状态、观看进度
- **OAuth 认证**：用户登录和授权
- **GraphQL 查询**：复杂数据查询支持

#### 2.2.2 Ani Server API
- **弹幕服务**：弹幕数据获取和发送
- **用户认证**：会话管理和令牌验证
- **番剧关系数据**：番剧关联信息
- **热度排行榜**：热门番剧推荐

#### 2.2.3 第三方 API
- **弹弹play**：弹幕数据源
- **各种数据源**：视频资源获取

### 2.3 视频播放和弹幕系统分析

#### 2.3.1 视频播放器架构
```kotlin
// 跨平台播放器抽象
interface MediampPlayer {
    // 播放控制接口
}

// 平台特定实现
// Android: ExoPlayer
// Desktop: VLC
// iOS: AVKit
// Web: 需要基于 HTML5 Video 重新实现
```

#### 2.3.2 弹幕系统架构
```kotlin
// DanmakuManager - 弹幕管理器
class DanmakuManager(
    val danmakuApi: ApiInvoker<DanmakuAniApi>
) {
    // 管理多个弹幕源
    private val providers = listOf(
        AniDanmakuProvider(danmakuApi),
        DandanplayDanmakuProvider(...)
    )
}

// DanmakuHost - 弹幕渲染组件
@Composable
fun DanmakuHost(
    state: DanmakuHostState,
    modifier: Modifier = Modifier,
    baseStyle: TextStyle = MaterialTheme.typography.bodyMedium
) {
    // Canvas 绘制的高性能弹幕引擎
}
```

## 3. Next.js Web 端架构设计

### 3.1 整体架构

```mermaid
graph TB
    subgraph "Next.js Web 前端"
        A[App Router] --> B[页面组件]
        B --> C[UI 组件库]
        C --> D[状态管理]
        D --> E[API 客户端]
    end

    subgraph "共享后端服务"
        F[Animeko 服务端]
        G[Bangumi API]
        H[弹弹play API]
    end

    subgraph "其他平台"
        I[Android/Desktop/iOS]
    end

    E --> F
    E --> G
    E --> H

    I --> F
    I --> G
    I --> H

    F --> G
    F --> H
```

### 3.2 技术栈选择

#### 3.2.1 核心框架
- **Next.js 15+**：React 全栈框架，使用 App Router
- **React 18+**：UI 框架，支持并发特性
- **TypeScript 5+**：类型安全的 JavaScript

#### 3.2.2 状态管理
- **Zustand**：轻量级状态管理
- **TanStack Query v5**：服务端状态管理和缓存
- **React Hook Form**：表单状态管理

#### 3.2.3 UI 和样式
- **Tailwind CSS**：原子化 CSS 框架
- **Radix UI**：无样式组件库
- **Framer Motion**：动画库
- **Lucide React**：图标库

#### 3.2.4 视频播放
- **Video.js**：HTML5 视频播放器
- **HLS.js**：HLS 流媒体支持
- **Canvas API**：弹幕渲染

#### 3.2.5 开发工具
- **ESLint + Prettier**：代码规范
- **Husky + lint-staged**：Git hooks
- **Vitest**：单元测试框架
- **Playwright**：端到端测试

### 3.3 基于 Animeko 架构的优化项目结构

基于 Animeko 项目的模块化架构，设计对应的 Next.js 项目结构：

```
animeko-web/
├── app/                           # Next.js App Router
│   ├── (auth)/                   # 认证相关页面组
│   │   ├── login/
│   │   │   ├── page.tsx          # 登录页面
│   │   │   └── oauth-callback/   # OAuth 回调处理
│   │   └── layout.tsx            # 认证布局
│   ├── (main)/                   # 主要页面组
│   │   ├── page.tsx              # 首页 - 对应 HomeScene
│   │   ├── explore/              # 探索页面 - 对应 ExploreScene
│   │   │   ├── page.tsx
│   │   │   ├── search/           # 搜索功能
│   │   │   └── trends/           # 热门排行
│   │   ├── collection/           # 收藏页面 - 对应 CollectionScene
│   │   │   ├── page.tsx
│   │   │   ├── [type]/           # 按类型分类收藏
│   │   │   └── history/          # 观看历史
│   │   ├── subject/              # 番剧详情 - 对应 SubjectScene
│   │   │   ├── [id]/
│   │   │   │   ├── page.tsx      # 番剧详情页
│   │   │   │   ├── episodes/     # 剧集列表
│   │   │   │   └── reviews/      # 评论区
│   │   │   └── components/       # 番剧相关组件
│   │   ├── watch/                # 观看页面 - 对应 VideoScene
│   │   │   ├── [subjectId]/
│   │   │   │   └── [episodeId]/
│   │   │   │       └── page.tsx  # 视频播放页
│   │   │   └── components/       # 播放器相关组件
│   │   ├── settings/             # 设置页面 - 对应 SettingsScene
│   │   │   ├── page.tsx
│   │   │   ├── profile/          # 个人资料
│   │   │   ├── danmaku/          # 弹幕设置
│   │   │   ├── player/           # 播放器设置
│   │   │   └── about/            # 关于页面
│   │   └── layout.tsx            # 主布局
│   ├── api/                      # API 路由
│   │   ├── auth/                 # 认证相关 API
│   │   │   ├── oauth/            # OAuth 处理
│   │   │   └── refresh/          # 令牌刷新
│   │   ├── proxy/                # API 代理
│   │   │   ├── bangumi/          # Bangumi API 代理
│   │   │   └── ani/              # Ani Server API 代理
│   │   └── health/               # 健康检查
│   ├── globals.css               # 全局样式
│   ├── layout.tsx                # 根布局
│   ├── loading.tsx               # 全局加载页面
│   ├── error.tsx                 # 全局错误页面
│   └── not-found.tsx             # 404 页面
├── components/                   # 组件库 - 对应 Compose UI 组件
│   ├── ui/                       # 基础 UI 组件 - 对应 AniTheme 组件
│   │   ├── button.tsx
│   │   ├── card.tsx
│   │   ├── dialog.tsx
│   │   ├── input.tsx
│   │   ├── avatar.tsx
│   │   ├── badge.tsx
│   │   ├── progress.tsx
│   │   ├── slider.tsx
│   │   ├── switch.tsx
│   │   ├── tabs.tsx
│   │   ├── toast.tsx
│   │   └── theme-provider.tsx
│   ├── features/                 # 功能组件 - 对应业务逻辑组件
│   │   ├── auth/                 # 认证相关组件
│   │   │   ├── login-form.tsx
│   │   │   ├── oauth-button.tsx
│   │   │   └── user-profile.tsx
│   │   ├── subject/              # 番剧相关组件
│   │   │   ├── subject-card.tsx
│   │   │   ├── subject-list.tsx
│   │   │   ├── subject-detail.tsx
│   │   │   ├── episode-list.tsx
│   │   │   ├── collection-button.tsx
│   │   │   └── rating-display.tsx
│   │   ├── video/                # 视频播放相关组件
│   │   │   ├── video-player.tsx
│   │   │   ├── video-controls.tsx
│   │   │   ├── danmaku-canvas.tsx
│   │   │   ├── danmaku-input.tsx
│   │   │   └── danmaku-settings.tsx
│   │   ├── search/               # 搜索相关组件
│   │   │   ├── search-bar.tsx
│   │   │   ├── search-results.tsx
│   │   │   └── search-filters.tsx
│   │   └── collection/           # 收藏相关组件
│   │       ├── collection-grid.tsx
│   │       ├── collection-stats.tsx
│   │       └── watch-history.tsx
│   ├── layout/                   # 布局组件 - 对应导航组件
│   │   ├── nav-bar.tsx           # 导航栏 - 对应 TopAppBar
│   │   ├── mobile-nav.tsx        # 移动端导航
│   │   ├── sidebar.tsx           # 侧边栏
│   │   ├── footer.tsx            # 页脚
│   │   └── breadcrumb.tsx        # 面包屑导航
│   └── common/                   # 通用组件
│       ├── loading-spinner.tsx
│       ├── error-boundary.tsx
│       ├── image-loader.tsx
│       └── seo-head.tsx
├── lib/                          # 工具库 - 对应 Kotlin 工具类
│   ├── api/                      # API 客户端 - 对应 data layer
│   │   ├── base.ts               # 基础 API 客户端
│   │   ├── bangumi.ts            # Bangumi API 客户端
│   │   ├── ani.ts                # Ani Server API 客户端
│   │   ├── dandanplay.ts         # 弹弹play API 客户端
│   │   └── index.ts              # API 客户端导出
│   ├── auth/                     # 认证相关工具 - 对应 SessionManager
│   │   ├── oauth.ts              # OAuth 处理
│   │   ├── token.ts              # 令牌管理
│   │   └── session.ts            # 会话管理
│   ├── player/                   # 播放器相关 - 对应 MediampPlayer
│   │   ├── player-session.ts     # 播放会话
│   │   ├── danmaku-manager.ts    # 弹幕管理器
│   │   └── video-utils.ts        # 视频工具函数
│   ├── hooks/                    # 自定义 Hooks
│   │   ├── use-auth.ts           # 认证 Hook
│   │   ├── use-navigation.ts     # 导航 Hook
│   │   ├── use-subject.ts        # 番剧数据 Hook
│   │   ├── use-danmaku.ts        # 弹幕 Hook
│   │   ├── use-player.ts         # 播放器 Hook
│   │   ├── use-collection.ts     # 收藏 Hook
│   │   ├── use-search.ts         # 搜索 Hook
│   │   └── use-theme.ts          # 主题 Hook
│   ├── utils/                    # 工具函数 - 对应 Kotlin utils
│   │   ├── format.ts             # 格式化工具
│   │   ├── date.ts               # 日期处理
│   │   ├── color.ts              # 颜色处理
│   │   ├── validation.ts         # 数据验证
│   │   ├── storage.ts            # 本地存储
│   │   ├── url.ts                # URL 处理
│   │   └── constants.ts          # 常量定义
│   ├── types/                    # 类型定义 - 对应 Kotlin data classes
│   │   ├── common.ts             # 通用类型
│   │   ├── subject.ts            # 番剧相关类型
│   │   ├── user.ts               # 用户相关类型
│   │   ├── auth.ts               # 认证相关类型
│   │   ├── danmaku.ts            # 弹幕相关类型
│   │   ├── api.ts                # API 响应类型
│   │   └── index.ts              # 类型导出
│   ├── config/                   # 配置文件
│   │   ├── env.ts                # 环境变量配置
│   │   ├── api.ts                # API 配置
│   │   ├── theme.ts              # 主题配置
│   │   └── constants.ts          # 应用常量
│   └── navigation.ts             # 路由配置 - 对应 NavRoutes
├── stores/                       # 状态管理 - 对应 Repository pattern
│   ├── auth.ts                   # 认证状态 - 对应 SessionManager
│   ├── subject.ts                # 番剧状态 - 对应 SubjectRepository
│   ├── danmaku.ts                # 弹幕状态 - 对应 DanmakuManager
│   ├── player.ts                 # 播放器状态
│   ├── collection.ts             # 收藏状态
│   ├── search.ts                 # 搜索状态
│   ├── settings.ts               # 设置状态
│   └── index.ts                  # Store 导出
├── styles/                       # 样式文件
│   ├── globals.css               # 全局样式
│   ├── components.css            # 组件样式
│   ├── themes/                   # 主题样式
│   │   ├── light.css
│   │   ├── dark.css
│   │   └── variables.css
│   └── animations.css            # 动画样式
├── public/                       # 静态资源
│   ├── icons/                    # 图标文件
│   ├── images/                   # 图片资源
│   ├── fonts/                    # 字体文件
│   └── manifest.json             # PWA 配置
├── docs/                         # 文档
│   ├── api.md                    # API 文档
│   ├── components.md             # 组件文档
│   └── deployment.md             # 部署文档
├── tests/                        # 测试文件
│   ├── __mocks__/                # Mock 文件
│   ├── components/               # 组件测试
│   ├── hooks/                    # Hook 测试
│   ├── utils/                    # 工具函数测试
│   └── setup.ts                  # 测试配置
├── .env.local                    # 本地环境变量
├── .env.example                  # 环境变量示例
├── next.config.js                # Next.js 配置
├── tailwind.config.js            # Tailwind CSS 配置
├── tsconfig.json                 # TypeScript 配置
├── package.json                  # 项目依赖
└── README.md                     # 项目说明
```

### 3.4 数据模型转换策略

#### 3.4.1 完整的 TypeScript 类型定义

基于 Animeko 项目的实际数据模型，提供完整的 TypeScript 类型定义：

```typescript
// lib/types/common.ts - 基础类型定义
export interface PackedDate {
  year: number;
  month: number; // 1-12
  dayOfMonth: number; // 1-31
}

export interface Tag {
  name: string;
  count: number;
  isCanonical: boolean; // 是否为公共标签
}

export interface RatingInfo {
  rank: number;
  total: number;
  count: Record<string, number>; // "1" to "10" -> count
  score: number;
}

export interface SubjectCollectionStats {
  wish: number;
  collect: number;
  doing: number;
  onHold: number;
  dropped: number;
}

// 枚举类型定义
export enum SubjectType {
  BOOK = 1,
  ANIME = 2,
  MUSIC = 3,
  GAME = 4,
  REAL = 6
}

export enum EpisodeType {
  MainStory = 0,
  SP = 1,
  OP = 2,
  ED = 3,
  PV = 4,
  MAD = 5,
  Other = 6
}

export enum CollectionType {
  WISH = 1,
  COLLECT = 2,
  DOING = 3,
  ON_HOLD = 4,
  DROPPED = 5
}

export enum DanmakuLocation {
  TOP = 'TOP',
  BOTTOM = 'BOTTOM',
  NORMAL = 'NORMAL' // 滚动弹幕
}

// lib/types/subject.ts - 番剧相关类型
export interface SubjectInfo {
  subjectId: number;
  subjectType: SubjectType;
  name: string;
  nameCn: string;
  summary: string;
  nsfw: boolean;
  imageLarge: string;
  totalEpisodes: number;
  airDate: PackedDate;
  tags: Tag[];
  aliases: string[];
  ratingInfo: RatingInfo;
  collectionStats: SubjectCollectionStats;
  // 计算属性
  displayName: string; // nameCn || name
  allNames: string[]; // [name, nameCn, ...aliases]
}

export interface LightSubjectInfo {
  subjectId: number;
  name: string;
  nameCn: string;
  imageLarge: string;
  displayName: string;
}

export interface EpisodeInfo {
  episodeId: number;
  type: EpisodeType | null;
  name: string;
  nameCn: string;
  airDate: PackedDate;
  comment: number;
  desc: string;
  sort: number; // EpisodeSort
  ep: number | null; // EpisodeSort
  displayName: string;
}

export interface LightEpisodeInfo {
  episodeId: number;
  name: string;
  nameCn: string;
  airDate: PackedDate;
  timezone: string; // TimeZone string
  sort: number;
  ep: number | null;
  displayName: string;
}

export interface SubjectCollection {
  subjectId: number;
  type: CollectionType;
  rate: number; // 用户评分 0-10
  comment: string;
  private: boolean;
  tags: string[];
  updatedAt: string; // ISO date string
}

// lib/types/user.ts - 用户相关类型
export interface SelfInfo {
  id: string; // UUID 转换为 string
  nickname: string;
  email: string | null;
  hasPassword: boolean;
  avatarUrl: string | null;
  bangumiUsername: string | null;
}

export interface SelfInfoDisplay {
  title: string;
  subtitle: string;
}

export interface UserInfo {
  id: number;
  username: string | null;
  nickname: string | null;
  avatarUrl: string | null;
  sign: string | null;
}

export interface BangumiUser {
  id: number;
  username: string;
  nickname: string;
  avatar: {
    large: string;
    medium: string;
    small: string;
  };
  sign: string;
  userGroup: number;
}

// lib/types/auth.ts - 认证相关类型
export interface AccessTokenPair {
  aniAccessToken: string;
  bangumiAccessToken: string | null;
  expiresAtMillis: number;
}

export interface Session {
  type: 'guest' | 'access_token';
  tokens?: AccessTokenPair;
}

export interface OAuthResult {
  tokens: AccessTokenPair;
  refreshToken: string;
}

export interface AuthToken {
  accessToken: string;
  refreshToken: string;
  expiresIn: number;
  tokenType: string;
}

// lib/types/danmaku.ts - 弹幕相关类型
export interface DanmakuInfo {
  id: string;
  serviceId: string; // DanmakuServiceId
  senderId: string;
  content: DanmakuContent;
  playTimeMillis: number;
  color: number;
  text: string;
  location: DanmakuLocation;
}

export interface DanmakuContent {
  playTimeMillis: number;
  color: number; // RGB color
  text: string;
  location: DanmakuLocation;
}

export interface DanmakuPost {
  episodeId: number;
  content: DanmakuContent;
}

export interface DanmakuFetchRequest {
  subjectId: number;
  episodeId: number;
  filename?: string;
  fileHash?: string;
  videoDuration?: number;
}

export interface DanmakuFetchResult {
  providerId: string;
  matchInfo: DanmakuMatchInfo;
  list: DanmakuInfo[];
}

export interface DanmakuMatchInfo {
  serviceId: string;
  episodeId: number;
  method: 'NoMatch' | 'ExactId' | 'ExactName' | 'FuzzyName';
}

// lib/types/api.ts - API 响应类型
export interface ApiResponse<T> {
  success: boolean;
  data: T;
  message?: string;
  error?: string;
}

export interface PaginatedResponse<T> {
  items: T[];
  total: number;
  page: number;
  pageSize: number;
  hasNext: boolean;
}

export interface GraphQLResponse<T> {
  data: T;
  errors?: Array<{
    message: string;
    locations?: Array<{ line: number; column: number }>;
    path?: string[];
  }>;
}

// 具体 API 响应类型
export type SubjectSearchResponse = PaginatedResponse<SubjectInfo>;
export type EpisodeListResponse = ApiResponse<EpisodeInfo[]>;
export type UserCollectionsResponse = PaginatedResponse<SubjectCollection>;
export type DanmakuListResponse = ApiResponse<DanmakuInfo[]>;
```

## 4. 代码复用策略

### 4.1 API 接口复用

虽然无法直接复用 Kotlin 代码，但可以通过以下方式最大化复用现有的业务逻辑：

#### 4.1.1 完整的 API 客户端实现

```typescript
// lib/api/base.ts - 基础 API 客户端
export class BaseApiClient {
  protected baseUrl: string;
  protected defaultHeaders: Record<string, string> = {
    'Content-Type': 'application/json',
  };

  constructor(baseUrl: string) {
    this.baseUrl = baseUrl;
  }

  protected async request<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<T> {
    const url = `${this.baseUrl}${endpoint}`;
    const config: RequestInit = {
      ...options,
      headers: {
        ...this.defaultHeaders,
        ...options.headers,
      },
    };

    try {
      const response = await fetch(url, config);

      if (!response.ok) {
        throw new ApiError(
          `HTTP ${response.status}: ${response.statusText}`,
          response.status,
          endpoint
        );
      }

      const contentType = response.headers.get('content-type');
      if (contentType?.includes('application/json')) {
        return await response.json();
      }

      return response.text() as unknown as T;
    } catch (error) {
      if (error instanceof ApiError) {
        throw error;
      }
      throw new ApiError(`Network error: ${error}`, 0, endpoint);
    }
  }

  protected setAuthToken(token: string) {
    this.defaultHeaders['Authorization'] = `Bearer ${token}`;
  }

  protected removeAuthToken() {
    delete this.defaultHeaders['Authorization'];
  }
}

export class ApiError extends Error {
  constructor(
    message: string,
    public statusCode: number,
    public endpoint: string
  ) {
    super(message);
    this.name = 'ApiError';
  }
}

// lib/api/bangumi.ts - Bangumi API 客户端
export class BangumiApiClient extends BaseApiClient {
  private nextBaseUrl = 'https://next.bgm.tv';

  constructor() {
    super('https://api.bgm.tv');
  }

  // 获取番剧详情
  async getSubjectInfo(subjectId: number): Promise<SubjectInfo> {
    const data = await this.request<any>(`/v0/subjects/${subjectId}`);
    return this.transformSubjectInfo(data);
  }

  // 获取剧集列表
  async getEpisodes(subjectId: number): Promise<EpisodeInfo[]> {
    const data = await this.request<any[]>(`/v0/subjects/${subjectId}/episodes`);
    return data.map(this.transformEpisodeInfo);
  }

  // 获取用户收藏
  async getUserCollections(
    userId: number,
    type?: SubjectType,
    limit = 50,
    offset = 0
  ): Promise<PaginatedResponse<SubjectCollection>> {
    const params = new URLSearchParams({
      limit: limit.toString(),
      offset: offset.toString(),
    });
    if (type) params.append('subject_type', type.toString());

    const data = await this.request<any>(
      `/v0/users/${userId}/collections?${params}`
    );

    return {
      items: data.data?.map(this.transformSubjectCollection) || [],
      total: data.total || 0,
      page: Math.floor(offset / limit) + 1,
      pageSize: limit,
      hasNext: (offset + limit) < (data.total || 0),
    };
  }

  // 更新收藏状态
  async updateCollection(
    subjectId: number,
    type: CollectionType,
    rate?: number,
    comment?: string,
    tags?: string[]
  ): Promise<void> {
    await this.request(`/v0/users/-/collections/${subjectId}`, {
      method: 'POST',
      body: JSON.stringify({
        type,
        rate,
        comment,
        tags,
      }),
    });
  }

  // 搜索番剧
  async searchSubjects(
    keyword: string,
    type?: SubjectType,
    limit = 25,
    offset = 0
  ): Promise<PaginatedResponse<SubjectInfo>> {
    const params = new URLSearchParams({
      keyword,
      limit: limit.toString(),
      offset: offset.toString(),
    });
    if (type) params.append('type', type.toString());

    const data = await this.request<any>(`/v0/search/subjects?${params}`);

    return {
      items: data.data?.map(this.transformSubjectInfo) || [],
      total: data.total || 0,
      page: Math.floor(offset / limit) + 1,
      pageSize: limit,
      hasNext: (offset + limit) < (data.total || 0),
    };
  }

  // GraphQL 查询
  async executeGraphQL<T>(
    query: string,
    variables?: Record<string, any>
  ): Promise<GraphQLResponse<T>> {
    return this.request<GraphQLResponse<T>>('/v0/graphql', {
      method: 'POST',
      body: JSON.stringify({ query, variables }),
    });
  }

  // OAuth 相关方法
  async getSelfInfo(): Promise<BangumiUser> {
    const data = await this.request<any>('/v0/me');
    return this.transformBangumiUser(data);
  }

  // 数据转换方法
  private transformSubjectInfo(data: any): SubjectInfo {
    return {
      subjectId: data.id,
      subjectType: data.type,
      name: data.name || '',
      nameCn: data.name_cn || '',
      summary: data.summary || '',
      nsfw: data.nsfw || false,
      imageLarge: data.images?.large || '',
      totalEpisodes: data.total_episodes || 0,
      airDate: this.transformPackedDate(data.date),
      tags: data.tags?.map((tag: any) => ({
        name: tag.name,
        count: tag.count,
        isCanonical: true,
      })) || [],
      aliases: data.infobox?.filter((item: any) =>
        item.key === 'alias'
      ).map((item: any) => item.value) || [],
      ratingInfo: {
        rank: data.rating?.rank || 0,
        total: data.rating?.total || 0,
        count: data.rating?.count || {},
        score: data.rating?.score || 0,
      },
      collectionStats: {
        wish: data.collection?.wish || 0,
        collect: data.collection?.collect || 0,
        doing: data.collection?.doing || 0,
        onHold: data.collection?.on_hold || 0,
        dropped: data.collection?.dropped || 0,
      },
      displayName: data.name_cn || data.name || '',
      allNames: [
        data.name,
        data.name_cn,
        ...(data.infobox?.filter((item: any) => item.key === 'alias')
          .map((item: any) => item.value) || [])
      ].filter(Boolean),
    };
  }

  private transformEpisodeInfo(data: any): EpisodeInfo {
    return {
      episodeId: data.id,
      type: data.type,
      name: data.name || '',
      nameCn: data.name_cn || '',
      airDate: this.transformPackedDate(data.airdate),
      comment: data.comment || 0,
      desc: data.desc || '',
      sort: data.sort || 0,
      ep: data.ep || null,
      displayName: data.name_cn || data.name || '',
    };
  }

  private transformSubjectCollection(data: any): SubjectCollection {
    return {
      subjectId: data.subject_id,
      type: data.type,
      rate: data.rate || 0,
      comment: data.comment || '',
      private: data.private || false,
      tags: data.tag || [],
      updatedAt: data.updated_at,
    };
  }

  private transformBangumiUser(data: any): BangumiUser {
    return {
      id: data.id,
      username: data.username,
      nickname: data.nickname,
      avatar: {
        large: data.avatar?.large || '',
        medium: data.avatar?.medium || '',
        small: data.avatar?.small || '',
      },
      sign: data.sign || '',
      userGroup: data.user_group || 0,
    };
  }

  private transformPackedDate(dateStr: string): PackedDate {
    if (!dateStr) {
      return { year: 0, month: 0, dayOfMonth: 0 };
    }

    const date = new Date(dateStr);
    return {
      year: date.getFullYear(),
      month: date.getMonth() + 1,
      dayOfMonth: date.getDate(),
    };
  }
}
```

// lib/api/ani.ts - Ani Server API 客户端
export class AniApiClient extends BaseApiClient {
  constructor() {
    super(process.env.NEXT_PUBLIC_ANI_API_URL || 'https://api.animeko.org');
  }

  // 弹幕相关 API
  async getDanmaku(episodeId: number): Promise<DanmakuInfo[]> {
    const data = await this.request<any[]>(`/api/danmaku/${episodeId}`);
    return data.map(this.transformDanmakuInfo);
  }

  async postDanmaku(danmaku: DanmakuPost): Promise<DanmakuInfo> {
    const data = await this.request<any>('/api/danmaku', {
      method: 'POST',
      body: JSON.stringify(danmaku),
    });
    return this.transformDanmakuInfo(data);
  }

  // 用户认证相关 API
  async refreshToken(refreshToken: string): Promise<OAuthResult> {
    const data = await this.request<any>('/api/auth/refresh', {
      method: 'POST',
      body: JSON.stringify({ refreshToken }),
    });
    return {
      tokens: {
        aniAccessToken: data.tokens.accessToken,
        bangumiAccessToken: data.tokens.bangumiAccessToken,
        expiresAtMillis: data.tokens.expiresAtMillis,
      },
      refreshToken: data.tokens.refreshToken,
    };
  }

  async getUserInfo(): Promise<SelfInfo> {
    const data = await this.request<any>('/api/user/me');
    return {
      id: data.id,
      nickname: data.nickname,
      email: data.email,
      hasPassword: data.hasPassword,
      avatarUrl: data.avatarUrl,
      bangumiUsername: data.bangumiUsername,
    };
  }

  // OAuth 相关 API
  async getOAuthRegisterLink(requestId: string): Promise<string> {
    const data = await this.request<{ url: string }>('/api/oauth/register', {
      method: 'POST',
      body: JSON.stringify({ requestId }),
    });
    return data.url;
  }

  async getOAuthBindLink(requestId: string): Promise<string> {
    const data = await this.request<{ url: string }>('/api/oauth/bind', {
      method: 'POST',
      body: JSON.stringify({ requestId }),
    });
    return data.url;
  }

  async getOAuthResult(requestId: string): Promise<OAuthResult | null> {
    try {
      const data = await this.request<any>(`/api/oauth/result/${requestId}`);
      if (!data.success) return null;

      return {
        tokens: {
          aniAccessToken: data.tokens.accessToken,
          bangumiAccessToken: data.tokens.bangumiAccessToken,
          expiresAtMillis: data.tokens.expiresAtMillis,
        },
        refreshToken: data.tokens.refreshToken,
      };
    } catch (error) {
      if (error instanceof ApiError && error.statusCode === 425) {
        return null; // 还未完成认证
      }
      throw error;
    }
  }

  // 番剧关系数据 API
  async getSubjectRelations(subjectId: number): Promise<any[]> {
    return this.request<any[]>(`/api/subjects/${subjectId}/relations`);
  }

  // 热度排行榜 API
  async getTrends(limit = 20): Promise<SubjectInfo[]> {
    const data = await this.request<any[]>(`/api/trends?limit=${limit}`);
    return data.map(this.transformSubjectInfo);
  }

  // 数据转换方法
  private transformDanmakuInfo(data: any): DanmakuInfo {
    return {
      id: data.id,
      serviceId: data.serviceId,
      senderId: data.senderId,
      content: {
        playTimeMillis: data.content.playTimeMillis,
        color: data.content.color,
        text: data.content.text,
        location: data.content.location as DanmakuLocation,
      },
      playTimeMillis: data.content.playTimeMillis,
      color: data.content.color,
      text: data.content.text,
      location: data.content.location as DanmakuLocation,
    };
  }

  private transformSubjectInfo(data: any): SubjectInfo {
    // 与 BangumiApiClient 类似的转换逻辑
    return {
      subjectId: data.subjectId,
      subjectType: data.subjectType,
      name: data.name,
      nameCn: data.nameCn,
      summary: data.summary,
      nsfw: data.nsfw,
      imageLarge: data.imageLarge,
      totalEpisodes: data.totalEpisodes,
      airDate: data.airDate,
      tags: data.tags || [],
      aliases: data.aliases || [],
      ratingInfo: data.ratingInfo,
      collectionStats: data.collectionStats,
      displayName: data.nameCn || data.name,
      allNames: [data.name, data.nameCn, ...data.aliases].filter(Boolean),
    };
  }
}

// lib/api/dandanplay.ts - 弹弹play API 客户端
export class DandanplayApiClient extends BaseApiClient {
  private appId: string;
  private appSecret: string;

  constructor(appId: string, appSecret: string) {
    super('https://api.dandanplay.net');
    this.appId = appId;
    this.appSecret = appSecret;
  }

  protected async request<T>(endpoint: string, options: RequestInit = {}): Promise<T> {
    // 添加弹弹play认证头
    const timestamp = Math.floor(Date.now() / 1000);
    const signature = this.generateSignature(endpoint, timestamp);

    const headers = {
      ...options.headers,
      'X-AppId': this.appId,
      'X-Timestamp': timestamp.toString(),
      'X-Signature': signature,
    };

    return super.request(endpoint, { ...options, headers });
  }

  async searchBangumi(keyword: string): Promise<any[]> {
    const params = new URLSearchParams({ keyword });
    return this.request<any[]>(`/api/v2/search/bangumi?${params}`);
  }

  async getBangumiDetails(bangumiId: number): Promise<any> {
    return this.request<any>(`/api/v2/bangumi/${bangumiId}`);
  }

  async getDanmaku(episodeId: number): Promise<DanmakuInfo[]> {
    const data = await this.request<any>(`/api/v2/comment/${episodeId}`);
    return data.comments?.map(this.transformDanmakuInfo) || [];
  }

  private generateSignature(path: string, timestamp: number): string {
    // 实现弹弹play签名算法
    const message = `${this.appId}${timestamp}${path}${this.appSecret}`;
    // 使用 crypto-js 或其他库生成 MD5 哈希
    return btoa(message); // 简化实现，实际需要 MD5
  }

  private transformDanmakuInfo(data: any): DanmakuInfo {
    return {
      id: data.cid?.toString() || '',
      serviceId: 'Dandanplay',
      senderId: data.uid?.toString() || '',
      content: {
        playTimeMillis: Math.floor(data.p * 1000),
        color: parseInt(data.c, 16),
        text: data.m,
        location: this.mapDanmakuMode(data.mode),
      },
      playTimeMillis: Math.floor(data.p * 1000),
      color: parseInt(data.c, 16),
      text: data.m,
      location: this.mapDanmakuMode(data.mode),
    };
  }

  private mapDanmakuMode(mode: number): DanmakuLocation {
    switch (mode) {
      case 1: return DanmakuLocation.NORMAL;
      case 4: return DanmakuLocation.BOTTOM;
      case 5: return DanmakuLocation.TOP;
      default: return DanmakuLocation.NORMAL;
    }
  }
}

#### 4.1.2 完整的状态管理实现

```typescript
// stores/auth.ts - 认证状态管理
import { create } from 'zustand';
import { persist, subscribeWithSelector } from 'zustand/middleware';
import { AniApiClient } from '@/lib/api/ani';
import { BangumiApiClient } from '@/lib/api/bangumi';

interface AuthState {
  // 状态
  session: Session;
  user: SelfInfo | null;
  isLoading: boolean;
  error: string | null;

  // 操作
  login: (tokens: AccessTokenPair, refreshToken: string) => Promise<void>;
  logout: () => Promise<void>;
  refreshSession: () => Promise<void>;
  updateUser: (user: Partial<SelfInfo>) => void;
  clearError: () => void;
}

const aniApi = new AniApiClient();
const bangumiApi = new BangumiApiClient();

export const useAuthStore = create<AuthState>()(
  subscribeWithSelector(
    persist(
      (set, get) => ({
        session: { type: 'guest' },
        user: null,
        isLoading: false,
        error: null,

        login: async (tokens: AccessTokenPair, refreshToken: string) => {
          set({ isLoading: true, error: null });

          try {
            // 设置 API 客户端的认证令牌
            aniApi.setAuthToken(tokens.aniAccessToken);
            if (tokens.bangumiAccessToken) {
              bangumiApi.setAuthToken(tokens.bangumiAccessToken);
            }

            // 获取用户信息
            const user = await aniApi.getUserInfo();

            set({
              session: { type: 'access_token', tokens },
              user,
              isLoading: false,
            });

            // 存储 refresh token（通过 persist 中间件自动处理）
            localStorage.setItem('refresh_token', refreshToken);

            // 启动自动刷新定时器
            startTokenRefreshTimer(tokens.expiresAtMillis);
          } catch (error) {
            set({
              error: error instanceof Error ? error.message : '登录失败',
              isLoading: false,
            });
            throw error;
          }
        },

        logout: async () => {
          set({ isLoading: true });

          try {
            // 清除 API 客户端的认证令牌
            aniApi.removeAuthToken();
            bangumiApi.removeAuthToken();

            // 清除本地存储
            localStorage.removeItem('refresh_token');

            // 停止自动刷新定时器
            stopTokenRefreshTimer();

            set({
              session: { type: 'guest' },
              user: null,
              isLoading: false,
              error: null,
            });
          } catch (error) {
            set({
              error: error instanceof Error ? error.message : '登出失败',
              isLoading: false,
            });
          }
        },

        refreshSession: async () => {
          const refreshToken = localStorage.getItem('refresh_token');
          if (!refreshToken) {
            throw new Error('No refresh token available');
          }

          set({ isLoading: true, error: null });

          try {
            const result = await aniApi.refreshToken(refreshToken);

            // 更新会话
            await get().login(result.tokens, result.refreshToken);
          } catch (error) {
            // 刷新失败，清除会话
            await get().logout();
            set({
              error: error instanceof Error ? error.message : '令牌刷新失败',
              isLoading: false,
            });
            throw error;
          }
        },

        updateUser: (userUpdate: Partial<SelfInfo>) => {
          set(state => ({
            user: state.user ? { ...state.user, ...userUpdate } : null,
          }));
        },

        clearError: () => {
          set({ error: null });
        },
      }),
      {
        name: 'auth-storage',
        partialize: (state) => ({
          session: state.session,
          user: state.user,
        }),
      }
    )
  )
);

// 自动刷新令牌的定时器
let refreshTimer: NodeJS.Timeout | null = null;

function startTokenRefreshTimer(expiresAtMillis: number) {
  stopTokenRefreshTimer();

  // 在过期前 7 天开始尝试刷新
  const refreshTime = expiresAtMillis - Date.now() - (7 * 24 * 60 * 60 * 1000);

  if (refreshTime > 0) {
    refreshTimer = setTimeout(async () => {
      try {
        await useAuthStore.getState().refreshSession();
      } catch (error) {
        console.error('Auto refresh failed:', error);
      }
    }, refreshTime);
  }
}

function stopTokenRefreshTimer() {
  if (refreshTimer) {
    clearTimeout(refreshTimer);
    refreshTimer = null;
  }
}

// 初始化时检查并恢复会话
if (typeof window !== 'undefined') {
  const state = useAuthStore.getState();
  if (state.session.type === 'access_token' && state.session.tokens) {
    const { tokens } = state.session;

    // 检查令牌是否过期
    if (tokens.expiresAtMillis > Date.now()) {
      // 设置 API 客户端的认证令牌
      aniApi.setAuthToken(tokens.aniAccessToken);
      if (tokens.bangumiAccessToken) {
        bangumiApi.setAuthToken(tokens.bangumiAccessToken);
      }

      // 启动自动刷新定时器
      startTokenRefreshTimer(tokens.expiresAtMillis);
    } else {
      // 令牌已过期，尝试刷新
      state.refreshSession().catch(() => {
        // 刷新失败，清除会话
        state.logout();
      });
    }
  }
}
```

// stores/subject.ts - 番剧状态管理
import { create } from 'zustand';
import { subscribeWithSelector } from 'zustand/middleware';
import { BangumiApiClient } from '@/lib/api/bangumi';
import { AniApiClient } from '@/lib/api/ani';

interface SubjectState {
  // 状态
  subjects: Map<number, SubjectInfo>;
  episodes: Map<number, EpisodeInfo[]>;
  collections: Map<number, SubjectCollection>;
  searchResults: SubjectInfo[];
  trends: SubjectInfo[];
  isLoading: boolean;
  error: string | null;

  // 操作
  fetchSubject: (id: number) => Promise<SubjectInfo>;
  fetchEpisodes: (subjectId: number) => Promise<EpisodeInfo[]>;
  fetchUserCollections: (userId: number) => Promise<void>;
  updateCollection: (subjectId: number, type: CollectionType, rate?: number) => Promise<void>;
  searchSubjects: (keyword: string, type?: SubjectType) => Promise<void>;
  fetchTrends: () => Promise<void>;
  clearError: () => void;
}

const bangumiApi = new BangumiApiClient();
const aniApi = new AniApiClient();

export const useSubjectStore = create<SubjectState>()(
  subscribeWithSelector((set, get) => ({
    subjects: new Map(),
    episodes: new Map(),
    collections: new Map(),
    searchResults: [],
    trends: [],
    isLoading: false,
    error: null,

    fetchSubject: async (id: number) => {
      const existing = get().subjects.get(id);
      if (existing) return existing;

      set({ isLoading: true, error: null });

      try {
        const subject = await bangumiApi.getSubjectInfo(id);

        set(state => ({
          subjects: new Map(state.subjects).set(id, subject),
          isLoading: false,
        }));

        return subject;
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : '获取番剧信息失败';
        set({ error: errorMessage, isLoading: false });
        throw error;
      }
    },

    fetchEpisodes: async (subjectId: number) => {
      const existing = get().episodes.get(subjectId);
      if (existing) return existing;

      set({ isLoading: true, error: null });

      try {
        const episodes = await bangumiApi.getEpisodes(subjectId);

        set(state => ({
          episodes: new Map(state.episodes).set(subjectId, episodes),
          isLoading: false,
        }));

        return episodes;
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : '获取剧集信息失败';
        set({ error: errorMessage, isLoading: false });
        throw error;
      }
    },

    fetchUserCollections: async (userId: number) => {
      set({ isLoading: true, error: null });

      try {
        const response = await bangumiApi.getUserCollections(userId);
        const collectionsMap = new Map<number, SubjectCollection>();

        response.items.forEach(collection => {
          collectionsMap.set(collection.subjectId, collection);
        });

        set({
          collections: collectionsMap,
          isLoading: false,
        });
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : '获取收藏信息失败';
        set({ error: errorMessage, isLoading: false });
        throw error;
      }
    },

    updateCollection: async (subjectId: number, type: CollectionType, rate?: number) => {
      set({ isLoading: true, error: null });

      try {
        await bangumiApi.updateCollection(subjectId, type, rate);

        // 更新本地状态
        set(state => {
          const newCollections = new Map(state.collections);
          const existing = newCollections.get(subjectId);

          newCollections.set(subjectId, {
            ...existing,
            subjectId,
            type,
            rate: rate || existing?.rate || 0,
            comment: existing?.comment || '',
            private: existing?.private || false,
            tags: existing?.tags || [],
            updatedAt: new Date().toISOString(),
          });

          return {
            collections: newCollections,
            isLoading: false,
          };
        });
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : '更新收藏状态失败';
        set({ error: errorMessage, isLoading: false });
        throw error;
      }
    },

    searchSubjects: async (keyword: string, type?: SubjectType) => {
      set({ isLoading: true, error: null });

      try {
        const response = await bangumiApi.searchSubjects(keyword, type);

        set({
          searchResults: response.items,
          isLoading: false,
        });
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : '搜索失败';
        set({ error: errorMessage, isLoading: false });
        throw error;
      }
    },

    fetchTrends: async () => {
      set({ isLoading: true, error: null });

      try {
        const trends = await aniApi.getTrends();

        set({
          trends,
          isLoading: false,
        });
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : '获取热门番剧失败';
        set({ error: errorMessage, isLoading: false });
        throw error;
      }
    },

    clearError: () => {
      set({ error: null });
    },
  }))
);

// stores/danmaku.ts - 弹幕状态管理
import { create } from 'zustand';
import { AniApiClient } from '@/lib/api/ani';
import { DandanplayApiClient } from '@/lib/api/dandanplay';

interface DanmakuState {
  // 状态
  danmakuMap: Map<number, DanmakuInfo[]>; // episodeId -> danmaku list
  isLoading: boolean;
  error: string | null;

  // 弹幕设置
  settings: {
    enabled: boolean;
    opacity: number;
    fontSize: number;
    speed: number;
    maxCount: number;
    filterRegex: string[];
  };

  // 操作
  fetchDanmaku: (episodeId: number) => Promise<DanmakuInfo[]>;
  postDanmaku: (episodeId: number, content: DanmakuContent) => Promise<void>;
  updateSettings: (settings: Partial<DanmakuState['settings']>) => void;
  clearError: () => void;
}

const aniApi = new AniApiClient();
const dandanplayApi = new DandanplayApiClient(
  process.env.NEXT_PUBLIC_DANDANPLAY_APP_ID || '',
  process.env.NEXT_PUBLIC_DANDANPLAY_APP_SECRET || ''
);

export const useDanmakuStore = create<DanmakuState>((set, get) => ({
  danmakuMap: new Map(),
  isLoading: false,
  error: null,
  settings: {
    enabled: true,
    opacity: 0.8,
    fontSize: 20,
    speed: 1.0,
    maxCount: 100,
    filterRegex: [],
  },

  fetchDanmaku: async (episodeId: number) => {
    const existing = get().danmakuMap.get(episodeId);
    if (existing) return existing;

    set({ isLoading: true, error: null });

    try {
      // 并行获取多个弹幕源的数据
      const [aniDanmaku, dandanplayDanmaku] = await Promise.allSettled([
        aniApi.getDanmaku(episodeId),
        dandanplayApi.getDanmaku(episodeId),
      ]);

      const allDanmaku: DanmakuInfo[] = [];

      if (aniDanmaku.status === 'fulfilled') {
        allDanmaku.push(...aniDanmaku.value);
      }

      if (dandanplayDanmaku.status === 'fulfilled') {
        allDanmaku.push(...dandanplayDanmaku.value);
      }

      // 按时间排序
      allDanmaku.sort((a, b) => a.playTimeMillis - b.playTimeMillis);

      set(state => ({
        danmakuMap: new Map(state.danmakuMap).set(episodeId, allDanmaku),
        isLoading: false,
      }));

      return allDanmaku;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '获取弹幕失败';
      set({ error: errorMessage, isLoading: false });
      throw error;
    }
  },

  postDanmaku: async (episodeId: number, content: DanmakuContent) => {
    set({ isLoading: true, error: null });

    try {
      const newDanmaku = await aniApi.postDanmaku({ episodeId, content });

      // 更新本地弹幕列表
      set(state => {
        const existingDanmaku = state.danmakuMap.get(episodeId) || [];
        const updatedDanmaku = [...existingDanmaku, newDanmaku]
          .sort((a, b) => a.playTimeMillis - b.playTimeMillis);

        return {
          danmakuMap: new Map(state.danmakuMap).set(episodeId, updatedDanmaku),
          isLoading: false,
        };
      });
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '发送弹幕失败';
      set({ error: errorMessage, isLoading: false });
      throw error;
    }
  },

  updateSettings: (newSettings: Partial<DanmakuState['settings']>) => {
    set(state => ({
      settings: { ...state.settings, ...newSettings },
    }));
  },

  clearError: () => {
    set({ error: null });
  },
}));

### 4.2 UI 组件设计复用

#### 4.2.1 完整的主题系统实现

```typescript
// lib/theme.ts - 主题配置
export const aniTheme = {
  colors: {
    primary: {
      50: '#eff6ff',
      100: '#dbeafe',
      200: '#bfdbfe',
      300: '#93c5fd',
      400: '#60a5fa',
      500: '#3b82f6',
      600: '#2563eb',
      700: '#1d4ed8',
      800: '#1e40af',
      900: '#1e3a8a',
      950: '#172554',
    },
    secondary: {
      50: '#f8fafc',
      100: '#f1f5f9',
      200: '#e2e8f0',
      300: '#cbd5e1',
      400: '#94a3b8',
      500: '#64748b',
      600: '#475569',
      700: '#334155',
      800: '#1e293b',
      900: '#0f172a',
      950: '#020617',
    },
    success: {
      50: '#f0fdf4',
      500: '#22c55e',
      900: '#14532d',
    },
    warning: {
      50: '#fffbeb',
      500: '#f59e0b',
      900: '#78350f',
    },
    error: {
      50: '#fef2f2',
      500: '#ef4444',
      900: '#7f1d1d',
    },
  },
  typography: {
    fontFamily: {
      sans: ['Inter', 'system-ui', 'sans-serif'],
      mono: ['JetBrains Mono', 'monospace'],
    },
    fontSize: {
      xs: ['0.75rem', { lineHeight: '1rem' }],
      sm: ['0.875rem', { lineHeight: '1.25rem' }],
      base: ['1rem', { lineHeight: '1.5rem' }],
      lg: ['1.125rem', { lineHeight: '1.75rem' }],
      xl: ['1.25rem', { lineHeight: '1.75rem' }],
      '2xl': ['1.5rem', { lineHeight: '2rem' }],
      '3xl': ['1.875rem', { lineHeight: '2.25rem' }],
      '4xl': ['2.25rem', { lineHeight: '2.5rem' }],
    },
  },
  spacing: {
    px: '1px',
    0: '0px',
    0.5: '0.125rem',
    1: '0.25rem',
    2: '0.5rem',
    3: '0.75rem',
    4: '1rem',
    5: '1.25rem',
    6: '1.5rem',
    8: '2rem',
    10: '2.5rem',
    12: '3rem',
    16: '4rem',
    20: '5rem',
    24: '6rem',
  },
  borderRadius: {
    none: '0px',
    sm: '0.125rem',
    DEFAULT: '0.25rem',
    md: '0.375rem',
    lg: '0.5rem',
    xl: '0.75rem',
    '2xl': '1rem',
    full: '9999px',
  },
  animation: {
    'fade-in': 'fadeIn 0.2s ease-in-out',
    'slide-up': 'slideUp 0.3s ease-out',
    'slide-down': 'slideDown 0.3s ease-out',
    'bounce-in': 'bounceIn 0.4s ease-out',
  },
};

// components/ui/theme-provider.tsx
'use client';

import { createContext, useContext, useEffect, useState } from 'react';

type Theme = 'light' | 'dark' | 'system';

interface ThemeContextType {
  theme: Theme;
  setTheme: (theme: Theme) => void;
  resolvedTheme: 'light' | 'dark';
}

const ThemeContext = createContext<ThemeContextType | undefined>(undefined);

export function ThemeProvider({
  children,
  defaultTheme = 'system',
  storageKey = 'ani-theme',
}: {
  children: React.ReactNode;
  defaultTheme?: Theme;
  storageKey?: string;
}) {
  const [theme, setTheme] = useState<Theme>(defaultTheme);
  const [resolvedTheme, setResolvedTheme] = useState<'light' | 'dark'>('light');

  useEffect(() => {
    // 从 localStorage 读取主题设置
    const stored = localStorage.getItem(storageKey) as Theme;
    if (stored) {
      setTheme(stored);
    }
  }, [storageKey]);

  useEffect(() => {
    const root = window.document.documentElement;

    const updateTheme = () => {
      let resolved: 'light' | 'dark';

      if (theme === 'system') {
        resolved = window.matchMedia('(prefers-color-scheme: dark)').matches
          ? 'dark'
          : 'light';
      } else {
        resolved = theme;
      }

      setResolvedTheme(resolved);

      root.classList.remove('light', 'dark');
      root.classList.add(resolved);
    };

    updateTheme();

    // 监听系统主题变化
    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
    mediaQuery.addEventListener('change', updateTheme);

    return () => mediaQuery.removeEventListener('change', updateTheme);
  }, [theme]);

  const handleSetTheme = (newTheme: Theme) => {
    setTheme(newTheme);
    localStorage.setItem(storageKey, newTheme);
  };

  return (
    <ThemeContext.Provider value={{
      theme,
      setTheme: handleSetTheme,
      resolvedTheme,
    }}>
      <div className="min-h-screen bg-background text-foreground transition-colors">
        {children}
      </div>
    </ThemeContext.Provider>
  );
}

export function useTheme() {
  const context = useContext(ThemeContext);
  if (!context) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  return context;
}
```

#### 4.2.3 完整的视频播放器组件实现

```typescript
// components/video/video-player.tsx - 视频播放器组件
'use client';

import { useRef, useEffect, useState, useCallback } from 'react';
import videojs from 'video.js';
import Player from 'video.js/dist/types/player';
import 'video.js/dist/video-js.css';
import { DanmakuCanvas } from './danmaku-canvas';
import { VideoControls } from './video-controls';
import { useDanmakuStore } from '@/stores/danmaku';

interface VideoPlayerProps {
  src: string;
  poster?: string;
  episodeId: number;
  autoplay?: boolean;
  onTimeUpdate?: (currentTime: number) => void;
  onDurationChange?: (duration: number) => void;
  onPlay?: () => void;
  onPause?: () => void;
  onEnded?: () => void;
}

export function VideoPlayer({
  src,
  poster,
  episodeId,
  autoplay = false,
  onTimeUpdate,
  onDurationChange,
  onPlay,
  onPause,
  onEnded,
}: VideoPlayerProps) {
  const videoRef = useRef<HTMLVideoElement>(null);
  const playerRef = useRef<Player | null>(null);
  const containerRef = useRef<HTMLDivElement>(null);

  const [isReady, setIsReady] = useState(false);
  const [isPlaying, setIsPlaying] = useState(false);
  const [currentTime, setCurrentTime] = useState(0);
  const [duration, setDuration] = useState(0);
  const [volume, setVolume] = useState(1);
  const [isFullscreen, setIsFullscreen] = useState(false);

  const {
    danmakuMap,
    settings: danmakuSettings,
    fetchDanmaku
  } = useDanmakuStore();

  // 初始化播放器
  useEffect(() => {
    if (!videoRef.current) return;

    const player = videojs(videoRef.current, {
      controls: false, // 使用自定义控件
      responsive: true,
      fluid: true,
      preload: 'metadata',
      sources: [{ src, type: 'video/mp4' }],
      poster,
      autoplay,
      playbackRates: [0.5, 0.75, 1, 1.25, 1.5, 2],
    });

    playerRef.current = player;

    // 播放器就绪
    player.ready(() => {
      setIsReady(true);
      setDuration(player.duration() || 0);
      setVolume(player.volume());
    });

    // 事件监听
    player.on('play', () => {
      setIsPlaying(true);
      onPlay?.();
    });

    player.on('pause', () => {
      setIsPlaying(false);
      onPause?.();
    });

    player.on('timeupdate', () => {
      const time = player.currentTime() || 0;
      setCurrentTime(time);
      onTimeUpdate?.(time);
    });

    player.on('durationchange', () => {
      const dur = player.duration() || 0;
      setDuration(dur);
      onDurationChange?.(dur);
    });

    player.on('volumechange', () => {
      setVolume(player.volume());
    });

    player.on('ended', () => {
      setIsPlaying(false);
      onEnded?.();
    });

    player.on('fullscreenchange', () => {
      setIsFullscreen(player.isFullscreen());
    });

    return () => {
      if (playerRef.current) {
        playerRef.current.dispose();
        playerRef.current = null;
      }
    };
  }, [src, poster, autoplay, onTimeUpdate, onDurationChange, onPlay, onPause, onEnded]);

  // 获取弹幕数据
  useEffect(() => {
    if (episodeId && isReady) {
      fetchDanmaku(episodeId).catch(console.error);
    }
  }, [episodeId, isReady, fetchDanmaku]);

  // 播放器控制方法
  const play = useCallback(() => {
    playerRef.current?.play();
  }, []);

  const pause = useCallback(() => {
    playerRef.current?.pause();
  }, []);

  const togglePlay = useCallback(() => {
    if (isPlaying) {
      pause();
    } else {
      play();
    }
  }, [isPlaying, play, pause]);

  const seek = useCallback((time: number) => {
    if (playerRef.current) {
      playerRef.current.currentTime(time);
    }
  }, []);

  const setVolumeLevel = useCallback((level: number) => {
    if (playerRef.current) {
      playerRef.current.volume(Math.max(0, Math.min(1, level)));
    }
  }, []);

  const toggleMute = useCallback(() => {
    if (playerRef.current) {
      playerRef.current.muted(!playerRef.current.muted());
    }
  }, []);

  const toggleFullscreen = useCallback(() => {
    if (playerRef.current) {
      if (isFullscreen) {
        playerRef.current.exitFullscreen();
      } else {
        playerRef.current.requestFullscreen();
      }
    }
  }, [isFullscreen]);

  const changePlaybackRate = useCallback((rate: number) => {
    if (playerRef.current) {
      playerRef.current.playbackRate(rate);
    }
  }, []);

  const danmakuList = danmakuMap.get(episodeId) || [];

  return (
    <div
      ref={containerRef}
      className="relative w-full bg-black rounded-lg overflow-hidden"
      style={{ aspectRatio: '16/9' }}
    >
      {/* Video Element */}
      <video
        ref={videoRef}
        className="video-js vjs-default-skin w-full h-full"
        data-setup="{}"
        onClick={togglePlay}
      />

      {/* 弹幕层 */}
      {danmakuSettings.enabled && (
        <DanmakuCanvas
          danmakuList={danmakuList}
          currentTime={currentTime}
          isPlaying={isPlaying}
          settings={danmakuSettings}
        />
      )}

      {/* 自定义控件 */}
      <VideoControls
        isPlaying={isPlaying}
        currentTime={currentTime}
        duration={duration}
        volume={volume}
        isFullscreen={isFullscreen}
        onPlay={play}
        onPause={pause}
        onSeek={seek}
        onVolumeChange={setVolumeLevel}
        onToggleMute={toggleMute}
        onToggleFullscreen={toggleFullscreen}
        onPlaybackRateChange={changePlaybackRate}
      />

      {/* 加载状态 */}
      {!isReady && (
        <div className="absolute inset-0 flex items-center justify-center bg-black/50">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-white"></div>
        </div>
      )}
    </div>
  );
}

// components/video/danmaku-canvas.tsx - 弹幕渲染组件
'use client';

import { useRef, useEffect, useState, useCallback } from 'react';
import { DanmakuInfo, DanmakuLocation } from '@/lib/types';

interface DanmakuCanvasProps {
  danmakuList: DanmakuInfo[];
  currentTime: number;
  isPlaying: boolean;
  settings: {
    opacity: number;
    fontSize: number;
    speed: number;
    maxCount: number;
    filterRegex: string[];
  };
}

interface ActiveDanmaku extends DanmakuInfo {
  x: number;
  y: number;
  width: number;
  startTime: number;
  duration: number;
  track: number;
}

export function DanmakuCanvas({
  danmakuList,
  currentTime,
  isPlaying,
  settings
}: DanmakuCanvasProps) {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const animationRef = useRef<number>();
  const [activeDanmaku, setActiveDanmaku] = useState<ActiveDanmaku[]>([]);
  const [canvasSize, setCanvasSize] = useState({ width: 800, height: 450 });

  // 过滤弹幕
  const filteredDanmaku = useCallback((danmaku: DanmakuInfo[]) => {
    if (settings.filterRegex.length === 0) return danmaku;

    return danmaku.filter(item => {
      return !settings.filterRegex.some(regex => {
        try {
          return new RegExp(regex, 'i').test(item.text);
        } catch {
          return false;
        }
      });
    });
  }, [settings.filterRegex]);

  // 计算弹幕轨道
  const calculateTrack = useCallback((
    danmaku: DanmakuInfo,
    existingDanmaku: ActiveDanmaku[]
  ): number => {
    const trackHeight = settings.fontSize + 4;
    const maxTracks = Math.floor(canvasSize.height / trackHeight);

    for (let track = 0; track < maxTracks; track++) {
      const trackDanmaku = existingDanmaku.filter(d => d.track === track);

      // 检查轨道是否有冲突
      const hasConflict = trackDanmaku.some(existing => {
        const timeOverlap = Math.abs(existing.playTimeMillis - danmaku.playTimeMillis) < 3000;
        return timeOverlap;
      });

      if (!hasConflict) {
        return track;
      }
    }

    return Math.floor(Math.random() * maxTracks);
  }, [settings.fontSize, canvasSize.height]);

  // 更新活跃弹幕
  useEffect(() => {
    const currentTimeMs = currentTime * 1000;
    const timeWindow = 5000; // 5秒窗口

    const visibleDanmaku = filteredDanmaku(danmakuList).filter(danmaku => {
      const timeDiff = danmaku.playTimeMillis - currentTimeMs;
      return timeDiff >= -1000 && timeDiff <= timeWindow;
    });

    const newActiveDanmaku: ActiveDanmaku[] = visibleDanmaku
      .slice(0, settings.maxCount)
      .map(danmaku => {
        const canvas = canvasRef.current;
        if (!canvas) return null;

        const ctx = canvas.getContext('2d');
        if (!ctx) return null;

        ctx.font = `${settings.fontSize}px Arial`;
        const width = ctx.measureText(danmaku.text).width;

        const track = calculateTrack(danmaku, activeDanmaku);
        const y = track * (settings.fontSize + 4) + settings.fontSize;

        return {
          ...danmaku,
          x: canvasSize.width,
          y,
          width,
          startTime: Date.now(),
          duration: 8000 / settings.speed, // 8秒基础时长
          track,
        };
      })
      .filter((item): item is ActiveDanmaku => item !== null);

    setActiveDanmaku(newActiveDanmaku);
  }, [currentTime, danmakuList, settings, canvasSize, filteredDanmaku, calculateTrack, activeDanmaku]);

  // 渲染弹幕
  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    const render = () => {
      ctx.clearRect(0, 0, canvas.width, canvas.height);

      activeDanmaku.forEach(danmaku => {
        const elapsed = Date.now() - danmaku.startTime;
        const progress = elapsed / danmaku.duration;

        if (progress > 1) return;

        let x: number, y: number;

        switch (danmaku.location) {
          case DanmakuLocation.TOP:
            x = (canvas.width - danmaku.width) / 2;
            y = danmaku.y;
            break;
          case DanmakuLocation.BOTTOM:
            x = (canvas.width - danmaku.width) / 2;
            y = canvas.height - danmaku.y;
            break;
          default: // NORMAL - 滚动弹幕
            x = canvas.width - (canvas.width + danmaku.width) * progress;
            y = danmaku.y;
            break;
        }

        // 设置样式
        ctx.font = `${settings.fontSize}px Arial`;
        ctx.fillStyle = `rgba(${(danmaku.color >> 16) & 255}, ${(danmaku.color >> 8) & 255}, ${danmaku.color & 255}, ${settings.opacity})`;
        ctx.strokeStyle = 'rgba(0, 0, 0, 0.8)';
        ctx.lineWidth = 2;

        // 绘制描边
        ctx.strokeText(danmaku.text, x, y);
        // 绘制文字
        ctx.fillText(danmaku.text, x, y);
      });

      if (isPlaying) {
        animationRef.current = requestAnimationFrame(render);
      }
    };

    if (isPlaying) {
      render();
    }

    return () => {
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current);
      }
    };
  }, [activeDanmaku, isPlaying, settings]);

  // 监听画布大小变化
  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const resizeObserver = new ResizeObserver(entries => {
      for (const entry of entries) {
        const { width, height } = entry.contentRect;
        setCanvasSize({ width, height });
        canvas.width = width;
        canvas.height = height;
      }
    });

    resizeObserver.observe(canvas);

    return () => resizeObserver.disconnect();
  }, []);

  return (
    <canvas
      ref={canvasRef}
      className="absolute inset-0 pointer-events-none"
      style={{ width: '100%', height: '100%' }}
    />
  );
}
```

### 4.3 业务逻辑复用

#### 4.3.1 播放器逻辑

```typescript
// lib/player/player-session.ts - 对应 PlayerSession
export class PlayerSession {
  private player: HTMLVideoElement;
  private danmakuManager: DanmakuManager;

  constructor(videoElement: HTMLVideoElement) {
    this.player = videoElement;
    this.danmakuManager = new DanmakuManager();
  }

  async loadMedia(url: string, episodeInfo: EpisodeInfo) {
    try {
      this.player.src = url;
      await this.player.load();

      // 加载弹幕
      const danmaku = await this.danmakuManager.loadDanmaku(episodeInfo.episodeId);
      this.setupDanmaku(danmaku);
    } catch (error) {
      console.error('Failed to load media:', error);
    }
  }

  private setupDanmaku(danmaku: DanmakuItem[]) {
    // 实现弹幕渲染逻辑
  }
}
```

#### 4.3.2 弹幕系统

```typescript
// lib/danmaku/danmaku-manager.ts - 对应 DanmakuManager
export class DanmakuManager {
  private providers: DanmakuProvider[] = [
    new AniDanmakuProvider(),
    new DandanplayDanmakuProvider(),
  ];

  async loadDanmaku(episodeId: number): Promise<DanmakuItem[]> {
    const results = await Promise.allSettled(
      this.providers.map(provider => provider.getDanmaku(episodeId))
    );

    return results
      .filter((result): result is PromiseFulfilledResult<DanmakuItem[]> =>
        result.status === 'fulfilled')
      .flatMap(result => result.value);
  }

  async postDanmaku(danmaku: DanmakuPost): Promise<void> {
    // 发送弹幕到服务器
    await aniApi.postDanmaku(danmaku);
  }
}

// components/features/danmaku/danmaku-canvas.tsx - 对应 DanmakuHost
export function DanmakuCanvas({
  danmaku,
  currentTime,
  isPlaying
}: DanmakuCanvasProps) {
  const canvasRef = useRef<HTMLCanvasElement>(null);

  useEffect(() => {
    if (!canvasRef.current || !isPlaying) return;

    const canvas = canvasRef.current;
    const ctx = canvas.getContext('2d')!;

    // 实现弹幕渲染逻辑
    const renderDanmaku = () => {
      ctx.clearRect(0, 0, canvas.width, canvas.height);

      const visibleDanmaku = danmaku.filter(item =>
        Math.abs(item.time - currentTime) < 5 // 5秒内的弹幕
      );

      visibleDanmaku.forEach(item => {
        // 绘制弹幕
        ctx.fillStyle = item.color;
        ctx.font = `${item.fontSize}px Arial`;
        ctx.fillText(item.text, item.x, item.y);
      });

      requestAnimationFrame(renderDanmaku);
    };

    renderDanmaku();
  }, [danmaku, currentTime, isPlaying]);

  return (
    <canvas
      ref={canvasRef}
      className="absolute inset-0 pointer-events-none"
      style={{ zIndex: 10 }}
    />
  );
}
```

## 5. 实施计划

### 5.1 分阶段开发计划

#### 5.1.1 阶段 1：项目基础搭建（2-3 周）

**目标：**建立 Next.js 项目基础架构

**主要任务：**
- 创建 Next.js 项目，配置 TypeScript
- 设置 Tailwind CSS 和 UI 组件库
- 实现基础布局和导航系统
- 配置状态管理（Zustand）
- 设置 API 客户端基础架构

**交付物：**
- 可运行的 Next.js 应用
- 基础 UI 组件库
- 导航系统
- 开发环境配置

#### 5.1.2 阶段 2：核心功能实现（4-6 周）

**目标：**实现番剧浏览和用户认证功能

**主要任务：**
- 实现 Bangumi API 集成
- 开发番剧列表和详情页面
- 实现用户认证系统（OAuth）
- 开发用户收藏管理功能
- 实现搜索功能

**交付物：**
- 番剧浏览功能
- 用户登录和收藏管理
- 搜索功能
- 响应式设计

#### 5.1.3 阶段 3：视频播放功能（3-4 周）

**目标：**实现视频播放和弹幕系统

**主要任务：**
- 集成 Video.js 播放器
- 实现弹幕渲染系统
- 开发播放控制界面
- 实现播放进度同步
- 添加播放历史记录

**交付物：**
- 完整的视频播放器
- 弹幕系统
- 播放进度管理
- 播放历史功能

#### 5.1.4 阶段 4：优化和部署（2-3 周）

**目标：**性能优化和生产部署

**主要任务：**
- 性能优化和代码分割
- SEO 优化
- PWA 支持
- 部署配置
- 监控和日志系统

**交付物：**
- 生产就绪的 Web 应用
- 部署文档
- 监控系统

### 5.2 技术实现细节

#### 5.2.1 视频播放器实现

```typescript
// components/features/player/video-player.tsx
import { useEffect, useRef, useState } from 'react';
import videojs from 'video.js';
import 'video.js/dist/video-js.css';

interface VideoPlayerProps {
  src: string;
  poster?: string;
  onTimeUpdate?: (currentTime: number) => void;
  onPlay?: () => void;
  onPause?: () => void;
}

export function VideoPlayer({ src, poster, onTimeUpdate, onPlay, onPause }: VideoPlayerProps) {
  const videoRef = useRef<HTMLVideoElement>(null);
  const playerRef = useRef<any>(null);

  useEffect(() => {
    if (!videoRef.current) return;

    // 初始化 Video.js
    playerRef.current = videojs(videoRef.current, {
      controls: true,
      responsive: true,
      fluid: true,
      sources: [{ src, type: 'application/x-mpegURL' }], // HLS 支持
      poster,
    });

    // 事件监听
    playerRef.current.on('timeupdate', () => {
      const currentTime = playerRef.current.currentTime();
      onTimeUpdate?.(currentTime);
    });

    playerRef.current.on('play', onPlay);
    playerRef.current.on('pause', onPause);

    return () => {
      if (playerRef.current) {
        playerRef.current.dispose();
      }
    };
  }, [src, poster, onTimeUpdate, onPlay, onPause]);

  return (
    <div className="relative">
      <video
        ref={videoRef}
        className="video-js vjs-default-skin w-full"
        data-setup="{}"
      />
    </div>
  );
}
```

#### 5.2.2 弹幕系统实现

```typescript
// components/features/danmaku/danmaku-renderer.tsx
import { useEffect, useRef, useCallback } from 'react';

interface DanmakuItem {
  id: string;
  text: string;
  time: number;
  color: string;
  type: 'scroll' | 'top' | 'bottom';
}

interface DanmakuRendererProps {
  danmaku: DanmakuItem[];
  currentTime: number;
  isPlaying: boolean;
  containerWidth: number;
  containerHeight: number;
}

export function DanmakuRenderer({
  danmaku,
  currentTime,
  isPlaying,
  containerWidth,
  containerHeight
}: DanmakuRendererProps) {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const animationRef = useRef<number>();

  const renderFrame = useCallback(() => {
    if (!canvasRef.current) return;

    const canvas = canvasRef.current;
    const ctx = canvas.getContext('2d')!;

    // 清空画布
    ctx.clearRect(0, 0, canvas.width, canvas.height);

    // 获取当前时间附近的弹幕
    const visibleDanmaku = danmaku.filter(item =>
      Math.abs(item.time - currentTime) < 5
    );

    // 渲染弹幕
    visibleDanmaku.forEach(item => {
      const progress = (currentTime - item.time) / 5; // 5秒内完成滚动

      if (progress >= 0 && progress <= 1) {
        ctx.fillStyle = item.color;
        ctx.font = '20px Arial';

        if (item.type === 'scroll') {
          const x = canvas.width - (canvas.width + 200) * progress;
          const y = Math.random() * (canvas.height - 50) + 25;
          ctx.fillText(item.text, x, y);
        } else if (item.type === 'top') {
          const x = (canvas.width - ctx.measureText(item.text).width) / 2;
          ctx.fillText(item.text, x, 30);
        } else if (item.type === 'bottom') {
          const x = (canvas.width - ctx.measureText(item.text).width) / 2;
          ctx.fillText(item.text, x, canvas.height - 30);
        }
      }
    });

    if (isPlaying) {
      animationRef.current = requestAnimationFrame(renderFrame);
    }
  }, [danmaku, currentTime, isPlaying]);

  useEffect(() => {
    if (isPlaying) {
      renderFrame();
    } else if (animationRef.current) {
      cancelAnimationFrame(animationRef.current);
    }

    return () => {
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current);
      }
    };
  }, [isPlaying, renderFrame]);

  return (
    <canvas
      ref={canvasRef}
      width={containerWidth}
      height={containerHeight}
      className="absolute inset-0 pointer-events-none"
      style={{ zIndex: 10 }}
    />
  );
}
```

### 5.3 部署架构

#### 5.3.1 Docker 配置

```dockerfile
# Dockerfile
FROM node:18-alpine AS base

# 安装依赖
FROM base AS deps
WORKDIR /app
COPY package.json package-lock.json ./
RUN npm ci --only=production

# 构建应用
FROM base AS builder
WORKDIR /app
COPY . .
COPY --from=deps /app/node_modules ./node_modules
RUN npm run build

# 生产镜像
FROM base AS runner
WORKDIR /app

ENV NODE_ENV production

RUN addgroup --system --gid 1001 nodejs
RUN adduser --system --uid 1001 nextjs

COPY --from=builder /app/public ./public
COPY --from=builder --chown=nextjs:nodejs /app/.next/standalone ./
COPY --from=builder --chown=nextjs:nodejs /app/.next/static ./.next/static

USER nextjs

EXPOSE 3000

ENV PORT 3000

CMD ["node", "server.js"]
```

#### 5.3.2 部署配置

```yaml
# docker-compose.yml
version: '3.8'

services:
  animeko-web:
    build: .
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - NEXT_PUBLIC_API_URL=https://api.animeko.org
      - NEXT_PUBLIC_BANGUMI_CLIENT_ID=${BANGUMI_CLIENT_ID}
    restart: unless-stopped

  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl
    depends_on:
      - animeko-web
    restart: unless-stopped
```

### 5.4 工作量评估

| 阶段 | 工作量 | 人员配置 | 技术风险 |
|------|--------|----------|----------|
| **阶段 1：基础搭建** | 2-3 周 | 1 名前端开发者 | 低 |
| **阶段 2：核心功能** | 4-6 周 | 1-2 名前端开发者 | 中 |
| **阶段 3：视频播放** | 3-4 周 | 1 名前端开发者 | 中 |
| **阶段 4：优化部署** | 2-3 周 | 1 名前端开发者 | 低 |
| **总计** | **11-16 周** | **1-2 名开发者** | **中等** |

## 6. 技术风险评估与缓解措施

### 6.1 主要技术风险

| 风险项 | 影响程度 | 概率 | 缓解措施 |
|--------|----------|------|----------|
| **视频播放兼容性** | 高 | 中 | 使用成熟的 Video.js，支持多种格式 |
| **弹幕性能问题** | 中 | 中 | Canvas 渲染 + 虚拟化技术 |
| **API 接口变更** | 中 | 低 | 版本控制 + 适配器模式 |
| **跨域问题** | 中 | 中 | 代理服务器 + CORS 配置 |
| **SEO 优化** | 低 | 低 | Next.js SSR + 元数据管理 |

### 6.2 性能优化策略

**前端优化：**
- 代码分割和懒加载
- 图片优化和 CDN
- 缓存策略优化
- Bundle 大小控制

**播放器优化：**
- 视频预加载策略
- 自适应码率
- 缓存机制
- 错误恢复

**弹幕优化：**
- Canvas 渲染优化
- 弹幕数量限制
- 内存管理
- 帧率控制

## 7. 技术方案优化总结

### 7.1 本次优化成果

基于 Animeko 项目的实际代码结构和架构，本次优化完成了以下工作：

#### 7.1.1 技术方案细化优化

**✅ 完成的优化项目：**

1. **精确的类型定义映射**
   - 基于实际的 Kotlin data classes 创建了完整的 TypeScript 类型定义
   - 涵盖 SubjectInfo、EpisodeInfo、SelfInfo、DanmakuInfo 等核心数据模型
   - 提供了枚举类型和复杂嵌套结构的准确转换

2. **完整的 API 客户端实现**
   - 实现了 BaseApiClient 基础类，提供统一的错误处理和重试机制
   - 完整的 BangumiApiClient，支持番剧搜索、用户收藏、GraphQL 查询等
   - 完整的 AniApiClient，支持弹幕管理、用户认证、OAuth 流程等
   - 集成了 DandanplayApiClient 作为弹幕数据源补充

3. **完善的状态管理架构**
   - 基于 Zustand 实现了认证状态管理，对应 SessionManager 功能
   - 实现了番剧状态管理，对应 SubjectRepository 模式
   - 实现了弹幕状态管理，对应 DanmakuManager 功能
   - 包含自动令牌刷新、错误处理、数据缓存等高级功能

#### 7.1.2 实现代码逻辑补充

**✅ 完成的核心组件：**

1. **完整的视频播放器组件**
   - 基于 Video.js 的专业级播放器实现
   - 支持自定义控件、全屏播放、倍速播放等功能
   - 集成弹幕渲染层，实现播放器与弹幕的同步

2. **高性能弹幕系统**
   - Canvas 渲染的弹幕组件，支持滚动、顶部、底部三种弹幕类型
   - 实现了弹幕轨道分配算法，避免弹幕重叠
   - 支持弹幕过滤、透明度调节、字体大小等个性化设置

3. **完整的主题系统**
   - 基于 Tailwind CSS 的主题配置
   - 支持亮色/暗色主题切换
   - 提供了完整的颜色系统和组件样式

4. **导航系统实现**
   - 对应 AniNavigator 的路由管理
   - 支持桌面端和移动端的响应式导航
   - 集成了面包屑导航和侧边栏功能

#### 7.1.3 项目结构优化

**✅ 完成的架构设计：**

1. **模块化目录结构**
   - 基于 Animeko 的分层架构设计了对应的 Next.js 项目结构
   - 清晰的功能模块划分：认证、番剧、播放器、弹幕、搜索、收藏等
   - 完整的组件分类：UI 组件、功能组件、布局组件、通用组件

2. **代码组织规范**
   - 提供了详细的文件命名规范和模块划分建议
   - 建立了类型定义、工具函数、配置文件的标准化结构
   - 包含了测试文件、文档、静态资源的完整组织方案

#### 7.1.4 集成方案完善

**✅ 完成的集成策略：**

1. **Bangumi OAuth 认证流程**
   - 详细的 OAuth 2.0 流程实现
   - 自动令牌刷新机制
   - 会话状态持久化

2. **多数据源弹幕系统**
   - 集成 Ani Server 和弹弹play 双重弹幕源
   - 弹幕数据的获取、缓存和同步机制
   - 支持弹幕发送和实时更新

3. **API 代理和错误处理**
   - 统一的 API 错误处理机制
   - 请求重试和超时处理
   - 数据转换和验证

### 7.2 技术方案优势

**选择 Next.js + TypeScript 方案的核心优势：**

1. **技术成熟度高**：Next.js 生态系统完善，开发效率高
2. **维护成本低**：相比 KMP 方案，长期维护更容易
3. **团队技能匹配**：Web 前端技术栈更容易找到合适人员
4. **用户体验优秀**：可以实现与原生 Web 应用相同的性能

**业务逻辑复用策略：**
- 通过 REST API 复用后端业务逻辑
- 数据模型转换为 TypeScript 接口
- 状态管理和 UI 组件重新设计但保持一致性

### 7.3 立即行动建议

基于本次优化的技术方案，建议按以下步骤实施：

**第一阶段（1-2 周）：项目初始化**
1. 使用本文档提供的项目结构创建 Next.js 项目
2. 配置 TypeScript、Tailwind CSS、ESLint 等开发工具
3. 实现基础 UI 组件库（Button、Card、Dialog 等）
4. 搭建主题系统和导航框架

**第二阶段（2-4 周）：核心功能实现**
1. 实现认证系统和 OAuth 流程
2. 集成 Bangumi API 和 Ani Server API 客户端
3. 实现番剧浏览、搜索、详情页面
4. 建立状态管理和数据缓存机制

**第三阶段（4-6 周）：播放器和弹幕系统**
1. 集成 Video.js 播放器组件
2. 实现弹幕渲染和交互功能
3. 集成多数据源弹幕系统
4. 实现播放历史和进度记录

**第四阶段（6-8 周）：功能完善和优化**
1. 实现收藏管理和评分功能
2. 添加个人设置和偏好配置
3. 性能优化和错误处理完善
4. 移动端响应式适配

**第五阶段（8-10 周）：测试和部署**
1. 编写单元测试和集成测试
2. 进行性能测试和用户体验优化
3. 配置 CI/CD 流程
4. 部署到生产环境

### 7.4 风险评估与应对

**主要风险：**
1. **API 兼容性**：Bangumi API 可能发生变化
2. **性能问题**：大量弹幕渲染可能影响性能
3. **跨域问题**：需要配置 CORS 或使用代理
4. **数据同步**：多个弹幕源的数据一致性

**应对策略：**
1. 实现 API 版本管理和降级策略，使用适配器模式处理 API 变更
2. 使用 Canvas 优化弹幕渲染，实现虚拟化和帧率控制
3. 配置 Next.js API 路由作为代理层，解决跨域问题
4. 建立数据校验和错误恢复机制，确保系统稳定性

### 7.5 后续扩展建议

**短期扩展（3-6 个月）：**
1. **PWA 支持**：添加离线缓存和推送通知
2. **社交功能**：评论系统和用户互动
3. **推荐算法**：基于用户行为的个性化推荐
4. **多语言支持**：国际化和本地化

**长期规划（6-12 个月）：**
1. **移动应用**：使用 React Native 或 Capacitor 打包移动应用
2. **桌面应用**：使用 Electron 或 Tauri 创建桌面版本
3. **AI 功能**：智能弹幕过滤和内容推荐
4. **区块链集成**：NFT 收藏品和去中心化存储

### 7.6 项目成功的关键因素

1. **采用成熟稳定的技术栈**：Next.js + TypeScript 提供了可靠的技术基础
2. **分阶段渐进式开发**：按功能模块逐步实现，降低开发风险
3. **重点关注用户体验**：保持与其他平台的功能一致性
4. **完善的错误处理和监控**：确保系统稳定性和可维护性
5. **充分的测试覆盖**：单元测试、集成测试、端到端测试
6. **持续的性能优化**：监控和优化关键性能指标

通过本次技术方案的深度优化，Animeko Web 端将能够提供与其他平台一致的用户体验，同时充分利用 Web 平台的优势，为用户带来更好的番剧观看体验。该方案不仅解决了之前 KMP 方案的技术难题，还提供了更加实用和可维护的解决方案。
